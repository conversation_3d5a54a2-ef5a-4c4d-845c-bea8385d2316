import { WidgetRenderingContextNew } from "@repo/shared/lib/types/editor";
import { motion } from "framer-motion";
import { useFramerEffects } from "@repo/shared/lib/hooks/useFramerEffects";
import { addWidgetMetadata } from "@repo/shared/lib/widget-metadata";
import { addPropertyControlsNew, ControlType } from "../../editor/property-controls-new";
import { useDynamicExpression } from "@repo/shared/lib/dynamic-values/hooks/useDynamicExpression";
import { AssetUrl } from "@repo/shared/lib/types/widgetSettings";
import { useMemo } from "react";

export const ImageWidget: React.FC<
  WidgetRenderingContextNew<any>
> = ({
  widgetId,
  settings,
  sceneId,
  resolveAssetUrl,
}) => {
  const { motionSettings } = useFramerEffects({
    effects: settings.effects,
    defaultVariant: { scale: 1, opacity: 1 },
  });

  // Resolve dynamic asset expression (handles both static URLs and dynamic expressions)
  const resolvedImageUrl = useDynamicExpression(
    settings?.image?.expression,
    ""
  );

    function validatedUrl(url: string) {
    try {
      new URL(url);
      return url;
    } catch (error) {
      return "";
    }
  };

  // Get the final image source
  const imageSrc = useMemo( () => {
    if (resolvedImageUrl) {
        return validatedUrl(resolveAssetUrl(JSON.parse(resolvedImageUrl) as AssetUrl)) 
    }
    return "https://picsum.photos/200/300";
  }, [resolvedImageUrl]);

  const getImageWidth = () => {
    const mode = settings.sizeMode;
    if (mode === "fixed" && settings.width) {
      return settings.width;
    }
    return settings.maxWidth || "100%";
  };

  const getImageHeight = () => {
    const mode = settings.sizeMode;
    if (mode === "fixed" && settings.height) {
      return settings.height;
    }
    return settings.maxHeight || "auto";
  };

  const containerStyle: React.CSSProperties = {
    display: "flex",
    overflow: "hidden",
    justifyContent: settings.horizontalAlignment || "center",
    alignItems: settings.verticalAlignment || "center",
    width: getImageWidth(),
    height: getImageHeight(),
    margin: settings.margin || "0px",
    padding: settings.padding || "0px",
  };

  const imageStyle: React.CSSProperties = {
    width: "100%",
    height: "100%",
    objectFit: settings.objectFit || "cover",
    borderStyle: settings.border?.style || "none",
    borderWidth: settings.border?.width || "0px",
    borderColor: settings.border?.color || "transparent",
    borderRadius: settings.border?.radius || "0px",
  };

  return (
    <div style={containerStyle}>
      <motion.img
        {...motionSettings}
        src={imageSrc}
        alt={settings.alt || ""}
        style={imageStyle}
      /> 
    </div>
  );
};

// Register image widget metadata
addWidgetMetadata(ImageWidget, {
  componentName: "ImageWidget",
  displayName: "Image",
  type: "block",
  description: "Display images with various sizing and alignment options",
  icon: "image",
});

// Register property controls
addPropertyControlsNew(ImageWidget, {
  // Image content (handles both static and dynamic sources)
  image: {
    type: ControlType.DynamicAsset,
    title: "Image",
    defaultValue: { expression: "https://picsum.photos/200/300" }
  },
  alt: {
    type: ControlType.Text,
    title: "Alt Text",
    defaultValue: "Image description"
  },
  objectFit: {
    type: ControlType.Enum,
    title: "Object Fit",
    defaultValue: "cover",
    options: [
      { label: "Contain", value: "contain" },
      { label: "Cover", value: "cover" },
      { label: "Fill", value: "fill" },
      { label: "None", value: "none" },
      { label: "Scale Down", value: "scale-down" }
    ]
  },

  // Alignment
  horizontalAlignment: {
    type: ControlType.Enum,
    title: "Horizontal Alignment",
    defaultValue: "center",
    options: [
      { label: "Left", value: "flex-start" },
      { label: "Center", value: "center" },
      { label: "Right", value: "flex-end" }
    ]
  },
  verticalAlignment: {
    type: ControlType.Enum,
    title: "Vertical Alignment",
    defaultValue: "center",
    options: [
      { label: "Top", value: "flex-start" },
      { label: "Center", value: "center" },
      { label: "Bottom", value: "flex-end" }
    ]
  },

  // Size
  sizeMode: {
    type: ControlType.Enum,
    title: "Size Mode",
    defaultValue: "fluid",
    options: [
      { label: "Fluid", value: "fluid" },
      { label: "Fixed", value: "fixed" }
    ]
  },
  width: {
    type: ControlType.Text,
    title: "Width",
    defaultValue: "200px",
    hidden: (props) => props.sizeMode !== "fixed"
  },
  height: {
    type: ControlType.Text,
    title: "Height",
    defaultValue: "300px",
    hidden: (props) => props.sizeMode !== "fixed"
  },
  maxWidth: {
    type: ControlType.Text,
    title: "Max Width",
    defaultValue: "100%",
    hidden: (props) => props.sizeMode === "fixed"
  },
  maxHeight: {
    type: ControlType.Text,
    title: "Max Height",
    defaultValue: "auto",
    hidden: (props) => props.sizeMode === "fixed"
  },

  // Layout
  padding: {
    type: ControlType.Padding,
    title: "Padding",
    defaultValue: "0px"
  },
  margin: {
    type: ControlType.Margin,
    title: "Margin",
    defaultValue: "0px"
  },

  // Border
  border: {
    type: ControlType.Border,
    title: "Border",
    defaultValue: {
      width: "0px",
      style: "none",
      color: "transparent",
      radius: "12px"
    }
  }
});
