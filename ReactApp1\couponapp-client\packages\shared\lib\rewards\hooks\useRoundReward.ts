import { useState, useEffect, useCallback } from 'react';
import { useRewards } from '../context/RewardsProvider';
import { RewardRoll, RewardRollResult } from '../types';
import { addRewardRollUpdateListener } from '../RewardsManager';

export interface RollResult extends RewardRollResult  {
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook to fetch a reward roll result for a specific round ID using the existing rewards system
 * @param roundId - The ID of the round to fetch the reward for
 * @param enabled - Whether the query should be enabled (default: true when roundId is provided)
 * @returns Query result containing the reward roll data
 */
export function useRollResult(roundId: string | null, enabled: boolean = true) {
  const { getPickResultByRoundId } = useRewards();
  const [rewardRoll, setRewardRollResult] = useState<RewardRoll | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchReward = useCallback(async () => {

    if(!enabled) {
      setIsLoading(false)
      return;
    }
    if (!roundId) {
      setRewardRollResult(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const result = await getPickResultByRoundId(roundId);
      setRewardRollResult(result);
      console.log("Reward roll result:", roundId, result)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch reward'));
      setRewardRollResult(null);
    } finally {
      setIsLoading(false);
    }
  }, [roundId, enabled]);

  useEffect(() => {
    console.log("New effect shit")
    fetchReward();
  }, [fetchReward]);

  // Listen for reward roll updates to automatically refresh when a reward is finished rolling
  useEffect(() => {
    if (!roundId || !enabled) return;

    const cleanup = addRewardRollUpdateListener((updatedRewardRoll) => {
      console.log("Reward roll updated:", updatedRewardRoll)
      if (updatedRewardRoll.roundId === roundId) {
        console.log("Setting reward roll result:", updatedRewardRoll);
        setRewardRollResult({...updatedRewardRoll});
        console.log("Reward roll updated via listener:", roundId, updatedRewardRoll);
      } else {
        console.log("Round ID mismatch:", updatedRewardRoll.roundId, "vs", roundId);
      }
    });

    return cleanup;
  }, [roundId, enabled]);

  return {
    ...rewardRoll?.result,
    isLoading,
    error,
  };
}