# Image Widget Data Source Plan

## Overview
Add data source capability to ImageWidget allowing asset selection from dynamic expressions instead of only static asset picker.

## Current State
- ImageWidget uses `ControlType.Asset` for static image selection
- Dynamic values system exists with `ValueSourcePicker` component
- Text widgets use `ControlType.DynamicText` for dynamic content

## Required Changes

### 1. Create DynamicAsset Type
**File**: `packages/shared/lib/types/widgetSettings.ts`
- Add `DynamicAsset` interface similar to `DynamicText`
- Structure: `{ expression: string }` for dynamic asset path resolution

### 2. Add DynamicAsset Control Type
**File**: `packages/shared/components/editor/property-controls-new.ts`
- Add `DynamicAsset = 'dynamicAsset'` to `ControlType` enum

### 3. Create DynamicAsset Property Control
**File**: `apps/dashboard/src/Pages/campaign/editor/_components/property-controls/dynamicAsset.tsx`
- Similar to `dynamicText.tsx` but for asset selection
- Use `ValueSourcePicker` for data source selection (no text input)
- Filter sources to asset-compatible types only
- Show selected data source path as read-only display

### 4. Register Property Control Handler
**File**: `apps/dashboard/src/Pages/campaign/editor/_components/property-controls/imports.tsx`
- Import and register `DynamicAsset` control handler

### 5. Update ImageWidget
**File**: `packages/shared/components/react-templates/blocks/imageWidget.tsx`
- Add `imageSource` property control with options: "static" | "dynamic"
- Add `dynamicImage` property using `ControlType.DynamicAsset`
- Show/hide controls based on `imageSource` selection
- Use `useDynamicExpression` hook when `imageSource === "dynamic"`
- Maintain backward compatibility with existing `image` property

### 6. Asset URL Resolution
- Ensure dynamic expressions can resolve to asset URLs
- Support both absolute URLs and asset IDs from data sources
- Handle asset resolution through existing `resolveAssetUrl` function

## Implementation Notes
- No text input needed - only data source selection
- Filter `ValueSourcePicker` to show only asset-compatible sources
- Maintain existing static asset picker functionality
- Follow existing dynamic values patterns from text widgets