import { AssetUrl } from '../types/widgetSettings';

// Reward Types
export type RewardType = 'coupon-code' | 'claimable-url';

export interface RewardDefinition {
  id: string;
  name: string;
  description: string;
  image?: AssetUrl;
  type: RewardType;
}

export interface RewardPool {
  id: string;
  name: string;
  rewards: RewardDefinition[];
}

// Game Round Types
export interface GameRoundResult {
  score?: number;
}

export interface GameEndData {
  score?: number;
}

export interface GameEventData {
  roundId: string;
  score?: number;
  currentScreen?: string;
}



export interface RewardRollResult {
  hasWon: boolean;
  reward?: RewardDefinition;
}

// Reward Roll Types
export interface RewardRoll {
  id: string;
  roundId: string;
  gameWidgetId: string;
  timestamp: number;
  result?: RewardRollResult;
}

export interface RewardHistory {
  rewardPoolId: string;
  rolls: RewardRoll[];
}

export interface RewardLoadingState {
  isLoading: boolean;
  rollId?: string;
  operation: 'rolling' | 'claiming' | 'fetching' | null;
}

// Enhanced Trigger System Types
export type RewardTrigger =
  | { when: "on_screen_change"; screens: string[] }
  | { when: "round_start" }
  | { when: "round_finish" }
  | { when: "game_finish" };

// Enhanced Reward Mechanics Types
export interface RewardMechanics {
  triggers: RewardTrigger[];
}

// Hook Return Types
export interface UseRewardsReturn {
  // State
  rewardHistory: RewardHistory;
  loadingState: RewardLoadingState;

  // Actions
  handleGameEvent: (
    triggerType: 'round_start' | 'round_finish' | 'game_finish',
    data: GameEventData
  ) => Promise<RewardRoll | null>;

  // Screen change specific handler
  handleScreenChange: (
    currentScreen: string,
    data: GameEventData
  ) => Promise<RewardRoll | null>;

  // Queries
  getPickResultByRoundId: (roundId: string) => Promise<RewardRoll >;
  getRewardHistory: () => Promise<RewardHistory>;
}
