import { ControlType, getPropertyControlsNew } from "@repo/shared/components/editor/property-controls-new";
import { addPropertyControlHandler } from "../../property-controls/propertyControlHandler";
import { Widget } from "@repo/shared/lib/types/editor";
import { useWidgetSettings } from "@/lib/hooks/useWidgetSettings";
import { Label } from "@repo/shared/components/ui/label";
import { ValueSourcePicker } from "@repo/shared/lib/dynamic-values/components/ValueSourcePicker";
import { DynamicAsset } from "@repo/shared/lib/types/widgetSettings";
import { useState } from "react";
import { Image, Sparkles, Link } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/shared/components/ui/popover";
import { Badge } from "@repo/shared/components/ui/badge";
import { Button } from "@repo/shared/components/ui/button";
import { Input } from "@repo/shared/components/ui/input";
import { AssetPicker } from "@repo/shared/components/editor/assetsManager";

function DynamicAssetPropertyControl(props: {widget: Widget, property: string}) {
    const { settings, updateSettings } = useWidgetSettings(props.widget)
    const propertyControls = getPropertyControlsNew(props.widget.componentName)
    const propertyControl = propertyControls?.[props.property]

    // Get current value or default
    const currentValue: DynamicAsset = settings[props.property] ?? propertyControl?.defaultValue ?? {
        expression: ""
    }

    const [isPopoverOpen, setIsPopoverOpen] = useState(false)
    const [sourceType, setSourceType] = useState<"url" | "asset" | "dynamic">(
        currentValue.expression.startsWith('{') ? "dynamic" :
        currentValue.expression.startsWith('http') ? "url" : "asset"
    )

    const handleExpressionChange = (expression: string) => {
        const newValue: DynamicAsset = {
            ...currentValue,
            expression
        }
        updateSettings({[props.property]: newValue})
    }

    const handleVariableSelect = (path: string) => {
        const variable = `{${path}}`
        handleExpressionChange(variable)
        setSourceType("dynamic")
        setIsPopoverOpen(false)
    }

    const handleAssetSelect = (assetUrl: any) => {
        handleExpressionChange(assetUrl.absoluteUrl || assetUrl)
        setSourceType("asset")
    }

    const handleUrlChange = (url: string) => {
        handleExpressionChange(url)
        setSourceType("url")
    }

    const clearSelection = () => {
        handleExpressionChange("")
        setSourceType("url")
    }

    return (
        <div className="space-y-3">
            <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">{propertyControl?.title ?? props.property}</Label>
            </div>

            {/* Source Type Selector */}
            <div className="flex gap-1 p-1 bg-muted rounded-md">
                <Button
                    variant={sourceType === "url" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSourceType("url")}
                    className="flex-1 h-8"
                >
                    <Link className="h-3 w-3 mr-1" />
                    URL
                </Button>
                <Button
                    variant={sourceType === "asset" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSourceType("asset")}
                    className="flex-1 h-8"
                >
                    <Image className="h-3 w-3 mr-1" />
                    Asset
                </Button>
                <Button
                    variant={sourceType === "dynamic" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSourceType("dynamic")}
                    className="flex-1 h-8"
                >
                    <Sparkles className="h-3 w-3 mr-1" />
                    Dynamic
                </Button>
            </div>

            {/* Input based on source type */}
            <div className="space-y-2">
                {sourceType === "url" && (
                    <Input
                        placeholder="Enter image URL..."
                        value={currentValue.expression.startsWith('{') ? "" : currentValue.expression}
                        onChange={(e) => handleUrlChange(e.target.value)}
                    />
                )}

                {sourceType === "asset" && (
                    <AssetPicker
                        onSelect={handleAssetSelect}
                        assetUrl={currentValue.expression.startsWith('{') ? undefined : { absoluteUrl: currentValue.expression }}
                        extensions={['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'avif']}
                    />
                )}

                {sourceType === "dynamic" && (
                    <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
                        <PopoverTrigger asChild>
                            {currentValue.expression.startsWith('{') ? (
                                <div className="flex items-center justify-between p-3 bg-muted rounded-md cursor-pointer hover:bg-muted/80 transition-colors">
                                    <div className="flex items-center gap-2">
                                        <Sparkles className="h-4 w-4 text-primary" />
                                        <span className="text-sm font-mono">{currentValue.expression}</span>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            clearSelection();
                                        }}
                                        className="h-6 px-2 text-xs"
                                    >
                                        Clear
                                    </Button>
                                </div>
                            ) : (
                                <div className="flex items-center justify-center gap-2 p-3 bg-muted/50 rounded-md border-2 border-dashed cursor-pointer hover:bg-muted/70 transition-colors">
                                    <Sparkles className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm text-muted-foreground">Select Dynamic Source</span>
                                </div>
                            )}
                        </PopoverTrigger>
                        <PopoverContent className="w-80 p-0" align="start">
                            <div className="border-b p-3">
                                <div className="flex items-center gap-2">
                                    <Sparkles className="h-4 w-4 text-primary" />
                                    <span className="font-medium">Dynamic Asset Sources</span>
                                </div>
                            </div>
                            <div className="p-4">
                                <ValueSourcePicker
                                    onSelect={handleVariableSelect}
                                    placeholder="Search for asset sources..."
                                    className="border-0"
                                />
                            </div>
                        </PopoverContent>
                    </Popover>
                )}
            </div>
        </div>
    )
}

addPropertyControlHandler(ControlType.DynamicAsset, (props: {widget: Widget, property: string}) => <DynamicAssetPropertyControl {...props}/>)
